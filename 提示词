你是一名大厂资深UI/UX设计专家，拥有丰富的移动端应用设计经验，精通苹果人机界面设计指南。请帮我完成一款名为`人生shi jian`iOS App的原型设计。请按照以下要求输出一套完整的高质量Figma APP原型图：
1. 设计目标
- 创建符合苹果人机界面指南(Human Interface Guidelines)的iOS原生风格设计
- 面向中草药爱好者和自然探索者，设计简洁直观的界面
- 确保原型图能直观展示APP的功能流程和用户体验
2. 用户需求分析
- 目标用户群体：希望能记录每天发生的事情,以时间线来展示的用户
- 用户痛点：当前市面上的app很少有能通过时间线的方式来记录生活
- 用户期望：能有一个能通过时间线来记录生活的app
3. 功能规划
- 主页：提供当前用户记录,以时间线的方式展示
- 人物: 展示出现在你时间线里面的人物
- 挑战: 记录一些用户希望挑战的事情,帮助自己自律,成长和变的更好
- 我：一些账户相关的信息,和设置等
4. 设计规范
- 使用最新的iOS设计元素和交互模式
- 遵循iPhone 6尺寸规格(宽度750px, 高度1624px)
- 采用自然、清新的配色方案，符合草本主题氛围
- 重视无障碍设计，确保文字对比度和交互区域大小合适
- 使用简洁清晰的图标和插图风格，融入自然元素
5. 原型图呈现要求
- 使用Figma创建所有设计稿
- 为每个功能设计一个到两个屏幕，如：登录/注册、主页、人物、挑战、我
- 每行最多排列三个屏幕，之后换行继续展示
- 为每个屏幕添加设备边框和阴影，不要遮住屏幕内的内容
- 为每个屏幕添加简短说明，解释其功能和设计考虑
6. 关键用户旅程原型屏幕
- 6.1 登录/注册屏幕
- 功能：用户可以通过邮箱、手机号或社交媒体账号登录/注册
- 设计考虑：使用简洁的表单设计，提供快速登录选项，符合iOS设计规范
- 6.2 主页屏幕
- 功能：展示用户时间线记录, 如果是新用户,那么就展示一个示例时间线. 时间线由一个一个的事件串联而成. 
- 设计考虑：采用卡片式布局，突出视觉重点，使用自然色调
- 6.3 人物
- 功能：出现在时间线里面的人物展示
- 设计考虑：使用网格布局，提供清晰的视觉层次，支持点击人物展示和该人物相关的时间
- 6.4 挑战
- 功能：展示,编辑,添加一些挑战事件,挑战能帮助用户培养更好的习惯和成长
- 设计考虑：采用上下滑动的单页布局，提供丰富的多媒体内容
- 6.5 我
- 功能：一些用户相关的信息,比如账户信息,还有设置,协议等
- 6.8 设计规范概述
- 配色方案：主色调为自然绿色(#4CAF50)，辅助色为棕色(#795548)和黄色(#FFC107)
- 图标：采用简洁的线性图标风格，融入自然元素
- 无障碍设计：确保文字对比度符合WCAG 2.1标准，交互区域大小合适
- 动效：使用微妙的过渡动画，提升用户体验但不干扰主要功能