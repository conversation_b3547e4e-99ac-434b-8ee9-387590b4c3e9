你是一名大厂资深UI/UX设计专家，拥有丰富的移动端应用设计经验，精通苹果人机界面设计指南。请帮我完成一款名为`百草集`iOS App的原型设计。请按照以下要求输出一套完整的高质量Figma APP原型图：
1. 设计目标
- 创建符合苹果人机界面指南(Human Interface Guidelines)的iOS原生风格设计
- 面向中草药爱好者和自然探索者，设计简洁直观的界面
- 确保原型图能直观展示APP的功能流程和用户体验
2. 用户需求分析
- 目标用户群体：
- 用户痛点：缺乏系统化的中草药知识、难以识别野外植物及其药用价值、无法记录和整理自己的植物观察
- 用户期望：直观的植物识别功能、个性化学习路径和推荐、社区互动和知识分享
3. 功能规划
- 主页：提供快速访问草本图鉴、观察记录和社区的入口
- 草本图鉴：分类别展示中草药，配有详细图文介绍和音频讲解
- 观察记录：记录用户在野外的植物观察，支持拍照识别和地理位置标记
- 配方推荐：基于用户兴趣推荐草本配方和使用方法
- 社区互动：分享观察、交流经验、获取专业指导
- 设置：个人信息管理、通知设置等
4. 设计规范
- 使用最新的iOS设计元素和交互模式
- 遵循iPhone 6尺寸规格(宽度750px, 高度1624px)
- 采用自然、清新的配色方案，符合草本主题氛围
- 重视无障碍设计，确保文字对比度和交互区域大小合适
- 使用简洁清晰的图标和插图风格，融入自然元素
5. 原型图呈现要求
- 使用Figma创建所有设计稿
- 为每个功能设计一个到两个屏幕，如：登录/注册、主页、草本图鉴、观察记录、配方推荐、社区互动、设置
- 每行最多排列三个屏幕，之后换行继续展示
- 为每个屏幕添加设备边框和阴影，不要遮住屏幕内的内容
- 为每个屏幕添加简短说明，解释其功能和设计考虑
6. 关键用户旅程原型屏幕
- 6.1 登录/注册屏幕
- 功能：用户可以通过邮箱、手机号或社交媒体账号登录/注册
- 设计考虑：使用简洁的表单设计，提供快速登录选项，符合iOS设计规范
- 6.2 主页屏幕
- 功能：展示主要功能入口，包括草本图鉴、观察记录、配方推荐和社区动态
- 设计考虑：采用卡片式布局，突出视觉重点，使用自然色调
- 6.3 草本图鉴屏幕
- 功能：分类展示中草药，支持搜索和筛选
- 设计考虑：使用网格布局，提供清晰的视觉层次，支持图片预览
- 6.4 植物详情屏幕
- 功能：展示植物的详细信息，包括图片、文字介绍、音频讲解
- 设计考虑：采用上下滑动的单页布局，提供丰富的多媒体内容
- 6.5 观察记录屏幕
- 功能：记录用户的植物观察，支持拍照识别和地理位置标记
- 设计考虑：使用时间线布局，提供直观的记录展示方式
- 6.6 配方推荐屏幕
- 功能：基于用户兴趣推荐草本配方，支持收藏和分享
- 设计考虑：采用卡片式布局，突出配方的视觉吸引力
- 6.7 社区互动屏幕
- 功能：用户可以发布动态、浏览社区内容、与其他用户互动
- 设计考虑：使用流式布局，支持点赞、评论等社交互动
- 6.8 设计规范概述
- 配色方案：主色调为自然绿色(#4CAF50)，辅助色为棕色(#795548)和黄色(#FFC107)
- 图标：采用简洁的线性图标风格，融入自然元素
- 无障碍设计：确保文字对比度符合WCAG 2.1标准，交互区域大小合适
- 动效：使用微妙的过渡动画，提升用户体验但不干扰主要功能