# 人生时间线 iOS App 设计规范

## 1. 设计理念
- 符合苹果人机界面设计指南 (Human Interface Guidelines)
- 简洁直观的界面设计
- 自然清新的视觉风格
- 注重用户体验和无障碍设计

## 2. 设备规格
- 目标设备：iPhone
- 设计尺寸：375×812pt (iPhone X/11/12/13 标准尺寸)
- 安全区域：考虑刘海屏和底部指示器
- 状态栏高度：44pt

## 3. 配色方案

### 主色调
- **主绿色**: #4CAF50 (自然生机的绿色)
- **深绿色**: #388E3C (用于强调和按钮按下状态)
- **浅绿色**: #C8E6C9 (用于背景和辅助元素)

### 辅助色
- **棕色**: #795548 (温暖的大地色调)
- **黄色**: #FFC107 (活力和提醒色)
- **橙色**: #FF9800 (警告和重要提示)

### 中性色
- **深灰色**: #212121 (主要文字)
- **中灰色**: #757575 (次要文字)
- **浅灰色**: #BDBDBD (分割线和边框)
- **背景灰**: #F5F5F5 (页面背景)
- **纯白色**: #FFFFFF (卡片和输入框背景)

## 4. 字体规范

### 字体族
- 中文：PingFang SC (系统默认)
- 英文：SF Pro Text/Display (系统默认)

### 字体大小层级
- **大标题**: 34pt (Bold) - 页面主标题
- **标题1**: 28pt (Bold) - 区块标题
- **标题2**: 22pt (Semibold) - 卡片标题
- **标题3**: 20pt (Semibold) - 小节标题
- **正文**: 17pt (Regular) - 主要内容
- **副文本**: 15pt (Regular) - 辅助信息
- **说明文字**: 13pt (Regular) - 提示和说明
- **小字**: 11pt (Regular) - 版权和细节

### 行高
- 标题：字体大小 × 1.2
- 正文：字体大小 × 1.4

## 5. 间距系统

### 基础间距单位：8pt
- **微间距**: 4pt (元素内部间距)
- **小间距**: 8pt (相关元素间距)
- **中间距**: 16pt (组件间距)
- **大间距**: 24pt (区块间距)
- **超大间距**: 32pt (页面区域间距)

### 页面边距
- **左右边距**: 16pt
- **顶部安全区**: 44pt + 16pt = 60pt
- **底部安全区**: 34pt + 16pt = 50pt

## 6. 组件规范

### 按钮
- **主按钮**: 高度48pt，圆角8pt，主绿色背景
- **次按钮**: 高度48pt，圆角8pt，白色背景+主绿色边框
- **文字按钮**: 高度44pt，无背景，主绿色文字
- **最小点击区域**: 44×44pt

### 输入框
- **高度**: 48pt
- **圆角**: 8pt
- **边框**: 1pt，浅灰色
- **聚焦状态**: 主绿色边框
- **内边距**: 左右16pt

### 卡片
- **圆角**: 12pt
- **阴影**: 0 2pt 8pt rgba(0,0,0,0.1)
- **内边距**: 16pt
- **背景**: 纯白色

### 时间线元素
- **时间轴线**: 2pt宽，主绿色
- **时间节点**: 12pt直径圆形，主绿色
- **事件卡片**: 圆角8pt，白色背景，左侧4pt主绿色边框

## 7. 图标规范

### 风格
- 线性图标风格
- 2pt线宽
- 24×24pt标准尺寸
- 圆角端点

### 主要图标
- 时间线：timeline图标
- 人物：person图标
- 挑战：target图标
- 个人：profile图标
- 添加：plus图标
- 设置：settings图标

## 8. 动效规范

### 过渡动画
- **持续时间**: 0.3秒
- **缓动函数**: ease-out
- **页面切换**: 右滑进入，左滑退出

### 微交互
- **按钮点击**: 0.1秒缩放动画
- **卡片展开**: 0.2秒高度变化
- **加载状态**: 旋转动画

## 9. 无障碍设计

### 对比度
- 正文文字对比度 ≥ 4.5:1
- 大文字对比度 ≥ 3:1
- 非文字元素对比度 ≥ 3:1

### 交互区域
- 最小点击区域：44×44pt
- 重要按钮：48×48pt或更大

### 语义化
- 使用语义化的标签和描述
- 支持VoiceOver屏幕阅读器
- 提供替代文本

## 10. 状态设计

### 空状态
- 插图 + 说明文字 + 引导按钮
- 温馨友好的提示语

### 加载状态
- 骨架屏或加载指示器
- 避免长时间等待

### 错误状态
- 清晰的错误信息
- 提供解决方案
- 重试机制
